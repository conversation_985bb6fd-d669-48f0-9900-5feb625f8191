// 智能纹理缓存管理器
// 使用LRU策略管理WebGL纹理内存

export class TextureCache {
  constructor(maxSize = 200 * 1024 * 1024) { // 默认200MB
    this.cache = new Map();
    this.maxSize = maxSize;
    this.currentSize = 0;
    this.accessOrder = new Map(); // 记录访问顺序
    this.gl = null;
  }
  
  // 设置WebGL上下文
  setGL(gl) {
    this.gl = gl;
  }
  
  // 计算纹理大小（字节）
  calculateTextureSize(width, height, format = 'RGB32F') {
    const formatSizes = {
      'RGB32F': 12, // 3 * 4 bytes
      'RGBA32F': 16, // 4 * 4 bytes
      'RGB16F': 6,   // 3 * 2 bytes
      'RGBA16F': 8,  // 4 * 2 bytes
      'RGB8': 3,     // 3 * 1 bytes
      'RGBA8': 4     // 4 * 1 bytes
    };
    
    return width * height * (formatSizes[format] || 12);
  }
  
  // 生成缓存键
  generateKey(groupName, width, height, channels) {
    return `${groupName}_${width}x${height}_${channels.join('+')}`;
  }
  
  // 获取纹理
  get(key) {
    if (this.cache.has(key)) {
      // 更新访问时间
      this.accessOrder.set(key, Date.now());
      return this.cache.get(key);
    }
    return null;
  }
  
  // 设置纹理
  set(key, texture, width, height, format = 'RGB32F') {
    const size = this.calculateTextureSize(width, height, format);
    
    // 检查是否需要清理空间
    this.ensureSpace(size);
    
    // 如果键已存在，先删除旧的
    if (this.cache.has(key)) {
      this.delete(key);
    }
    
    // 添加新纹理
    this.cache.set(key, {
      texture,
      size,
      width,
      height,
      format,
      createdAt: Date.now()
    });
    
    this.accessOrder.set(key, Date.now());
    this.currentSize += size;
    
    console.log(`Texture cached: ${key}, Size: ${(size / 1024 / 1024).toFixed(2)}MB, Total: ${(this.currentSize / 1024 / 1024).toFixed(2)}MB`);
  }
  
  // 删除纹理
  delete(key) {
    if (this.cache.has(key)) {
      const entry = this.cache.get(key);
      
      // 删除WebGL纹理
      if (this.gl && entry.texture) {
        this.gl.deleteTexture(entry.texture);
      }
      
      this.currentSize -= entry.size;
      this.cache.delete(key);
      this.accessOrder.delete(key);
      
      console.log(`Texture removed: ${key}, Freed: ${(entry.size / 1024 / 1024).toFixed(2)}MB`);
    }
  }
  
  // 确保有足够空间
  ensureSpace(requiredSize) {
    while (this.currentSize + requiredSize > this.maxSize && this.cache.size > 0) {
      // 找到最久未访问的纹理
      let oldestKey = null;
      let oldestTime = Date.now();
      
      for (const [key, time] of this.accessOrder) {
        if (time < oldestTime) {
          oldestTime = time;
          oldestKey = key;
        }
      }
      
      if (oldestKey) {
        console.log(`Cache full, removing oldest texture: ${oldestKey}`);
        this.delete(oldestKey);
      } else {
        break;
      }
    }
  }
  
  // 清理所有缓存
  clear() {
    for (const key of this.cache.keys()) {
      this.delete(key);
    }
    this.currentSize = 0;
  }
  
  // 获取缓存统计信息
  getStats() {
    return {
      totalEntries: this.cache.size,
      currentSize: this.currentSize,
      maxSize: this.maxSize,
      usagePercent: (this.currentSize / this.maxSize * 100).toFixed(2),
      entries: Array.from(this.cache.entries()).map(([key, entry]) => ({
        key,
        size: entry.size,
        dimensions: `${entry.width}x${entry.height}`,
        format: entry.format,
        age: Date.now() - entry.createdAt,
        lastAccess: Date.now() - this.accessOrder.get(key)
      }))
    };
  }
  
  // 预热缓存（预加载常用纹理）
  async preload(textureConfigs) {
    for (const config of textureConfigs) {
      if (!this.cache.has(config.key)) {
        // 这里可以实现预加载逻辑
        console.log(`Preloading texture: ${config.key}`);
      }
    }
  }
  
  // 设置最大缓存大小
  setMaxSize(newMaxSize) {
    this.maxSize = newMaxSize;
    this.ensureSpace(0); // 触发清理检查
  }
  
  // 获取内存使用情况
  getMemoryUsage() {
    return {
      used: this.currentSize,
      available: this.maxSize - this.currentSize,
      total: this.maxSize,
      percentage: (this.currentSize / this.maxSize) * 100
    };
  }
}

// 创建全局缓存实例
export const globalTextureCache = new TextureCache();

// 纹理压缩工具
export class TextureCompressor {
  static createMipmaps(gl, originalTexture, width, height, levels = 4) {
    const mipmaps = [];
    
    for (let level = 0; level < levels; level++) {
      const mipWidth = Math.max(1, width >> level);
      const mipHeight = Math.max(1, height >> level);
      
      // 创建帧缓冲区用于渲染到纹理
      const framebuffer = gl.createFramebuffer();
      const mipTexture = gl.createTexture();
      
      gl.bindTexture(gl.TEXTURE_2D, mipTexture);
      gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGB32F, mipWidth, mipHeight, 0, gl.RGB, gl.FLOAT, null);
      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
      
      gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);
      gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, mipTexture, 0);
      
      // 设置视口
      gl.viewport(0, 0, mipWidth, mipHeight);
      
      // 这里需要渲染原始纹理到当前mipmap级别
      // 实际实现需要一个简单的缩放着色器
      
      mipmaps.push({
        texture: mipTexture,
        width: mipWidth,
        height: mipHeight,
        level
      });
      
      gl.deleteFramebuffer(framebuffer);
    }
    
    return mipmaps;
  }
  
  // 根据缩放级别选择合适的mipmap
  static selectMipmap(mipmaps, scale) {
    if (!mipmaps || mipmaps.length === 0) return null;
    
    // 根据缩放比例选择合适的mipmap级别
    const level = Math.floor(Math.log2(1 / scale));
    const clampedLevel = Math.max(0, Math.min(level, mipmaps.length - 1));
    
    return mipmaps[clampedLevel];
  }
}
