<template>
  <div class="webgl-processor">
    <canvas ref="canvas"></canvas>
  </div>
</template>

<script>
export default {
  props: {
    image: {
      type: Image,
      required: true
    },
    contrast: {
      type: Number,
      default: 100
    },
    brightness: {
      type: Number,
      default: 100
    },
    hue: {
      type: Number,
      default: 0
    },
    saturation: {
      type: Number,
      default: 100
    },
    sharpen: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      gl: null,
      program: null,
      texture: null,
      vertexBuffer: null,
      texCoordBuffer: null,
      uniforms: {}
    };
  },
  watch: {
    contrast() { this.updateUniforms(); },
    brightness() { this.updateUniforms(); },
    hue() { this.updateUniforms(); },
    saturation() { this.updateUniforms(); },
    sharpen() { this.updateUniforms(); }
  },
  mounted() {
    this.initWebGL();
    this.createShaders();
    this.createBuffers();
    this.createTexture();
    this.updateUniforms();
    this.render();
  },
  methods: {
    initWebGL() {
      const canvas = this.$refs.canvas;
      if (!canvas) {
        console.error('Canvas element not found');
        return;
      }

      canvas.width = this.image.width;
      canvas.height = this.image.height;
      
      this.gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!this.gl) {
        console.error('WebGL not supported');
        return;
      }

      // 检查 WebGL 扩展支持
      const extensions = this.gl.getSupportedExtensions();
      if (!extensions.includes('OES_texture_float')) {
        console.warn('OES_texture_float extension not supported');
      }
    },
    createShaders() {
      const gl = this.gl;
      
      // 顶点着色器
      const vertexShader = gl.createShader(gl.VERTEX_SHADER);
      gl.shaderSource(vertexShader, `
        attribute vec2 a_position;
        attribute vec2 a_texCoord;
        varying vec2 v_texCoord;
        void main() {
          gl_Position = vec4(a_position, 0, 1);
          v_texCoord = a_texCoord;
        }
      `);
      gl.compileShader(vertexShader);

      // 检查顶点着色器编译状态
      if (!gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS)) {
        console.error('顶点着色器编译错误:', gl.getShaderInfoLog(vertexShader));
        gl.deleteShader(vertexShader);
        return;
      }

      // 片段着色器
      const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
      gl.shaderSource(fragmentShader, `
        precision mediump float;
        uniform sampler2D u_image;
        uniform float u_contrast;
        uniform float u_brightness;
        uniform float u_hue;
        uniform float u_saturation;
        uniform float u_sharpen;
        varying vec2 v_texCoord;

        vec3 rgb2hsv(vec3 c) {
          vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
          vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
          vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
          float d = q.x - min(q.w, q.y);
          float e = 1.0e-10;
          return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
        }

        vec3 hsv2rgb(vec3 c) {
          vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
          vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
          return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
        }

        void main() {
          vec4 color = texture2D(u_image, v_texCoord);
          
          // 对比度和亮度调整
          color.rgb = (color.rgb - 0.5) * (u_contrast / 100.0) + 0.5;
          color.rgb += (u_brightness - 100.0) / 100.0;
          
          // HSV 调整
          vec3 hsv = rgb2hsv(color.rgb);
          hsv.x = mod(hsv.x + u_hue / 360.0, 1.0);
          hsv.y *= u_saturation / 100.0;
          color.rgb = hsv2rgb(hsv);
          
          // 锐化
          if (u_sharpen > 0.0) {
            // 使用固定的偏移量进行锐化
            vec4 center = color;
            vec4 top = texture2D(u_image, v_texCoord + vec2(0.0, 0.001));
            vec4 bottom = texture2D(u_image, v_texCoord - vec2(0.0, 0.001));
            vec4 left = texture2D(u_image, v_texCoord - vec2(0.001, 0.0));
            vec4 right = texture2D(u_image, v_texCoord + vec2(0.001, 0.0));
            
            color = center * (1.0 + 4.0 * u_sharpen) - 
                   (top + bottom + left + right) * u_sharpen;
          }
          
          gl_FragColor = color;
        }
      `);
      gl.compileShader(fragmentShader);

      // 检查片段着色器编译状态
      if (!gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS)) {
        console.error('片段着色器编译错误:', gl.getShaderInfoLog(fragmentShader));
        gl.deleteShader(vertexShader);
        gl.deleteShader(fragmentShader);
        return;
      }

      // 创建程序
      this.program = gl.createProgram();
      gl.attachShader(this.program, vertexShader);
      gl.attachShader(this.program, fragmentShader);
      gl.linkProgram(this.program);

      // 检查程序链接状态
      if (!gl.getProgramParameter(this.program, gl.LINK_STATUS)) {
        console.error('着色器程序链接错误:', gl.getProgramInfoLog(this.program));
        gl.deleteProgram(this.program);
        gl.deleteShader(vertexShader);
        gl.deleteShader(fragmentShader);
        return;
      }

      gl.useProgram(this.program);

      // 获取 uniform 位置
      this.uniforms = {
        image: gl.getUniformLocation(this.program, 'u_image'),
        contrast: gl.getUniformLocation(this.program, 'u_contrast'),
        brightness: gl.getUniformLocation(this.program, 'u_brightness'),
        hue: gl.getUniformLocation(this.program, 'u_hue'),
        saturation: gl.getUniformLocation(this.program, 'u_saturation'),
        sharpen: gl.getUniformLocation(this.program, 'u_sharpen')
      };

      // 检查 uniform 位置是否有效
      for (const [name, location] of Object.entries(this.uniforms)) {
        if (location === null) {
          console.error(`无法获取 uniform 位置: ${name}`);
        }
      }

      // 清理着色器对象
      gl.deleteShader(vertexShader);
      gl.deleteShader(fragmentShader);
    },
    createBuffers() {
      const gl = this.gl;
      
      // 顶点缓冲区
      this.vertexBuffer = gl.createBuffer();
      gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
      gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([
        -1, -1,
         1, -1,
        -1,  1,
         1,  1
      ]), gl.STATIC_DRAW);

      // 纹理坐标缓冲区 - 修改纹理坐标以翻转图像
      this.texCoordBuffer = gl.createBuffer();
      gl.bindBuffer(gl.ARRAY_BUFFER, this.texCoordBuffer);
      gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([
        0, 1,  // 左下
        1, 1,  // 右下
        0, 0,  // 左上
        1, 0   // 右上
      ]), gl.STATIC_DRAW);
    },
    createTexture() {
      const gl = this.gl;
      if (!gl) return;
      
      this.texture = gl.createTexture();
      if (!this.texture) {
        console.error('Failed to create texture');
        return;
      }

      gl.bindTexture(gl.TEXTURE_2D, this.texture);
      
      // 设置纹理参数
      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
      gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
      
      // 上传图像数据
      gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, this.image);
      
      // 检查纹理是否成功创建
      const error = gl.getError();
      if (error !== gl.NO_ERROR) {
        console.error('Error creating texture:', error);
      }
    },
    updateUniforms() {
      const gl = this.gl;
      if (!gl || !this.program) return;

      try {
        gl.useProgram(this.program);
        gl.uniform1f(this.uniforms.contrast, this.contrast);
        gl.uniform1f(this.uniforms.brightness, this.brightness);
        gl.uniform1f(this.uniforms.hue, this.hue);
        gl.uniform1f(this.uniforms.saturation, this.saturation);
        gl.uniform1f(this.uniforms.sharpen, this.sharpen);
        this.render();
      } catch (e) {
        console.error('Error updating uniforms:', e);
      }
    },
    render() {
      const gl = this.gl;
      if (!gl || !this.program) return;
      
      try {
        gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);
        gl.clearColor(0, 0, 0, 0);
        gl.clear(gl.COLOR_BUFFER_BIT);

        // 设置顶点属性
        gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
        const positionLocation = gl.getAttribLocation(this.program, 'a_position');
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

        // 设置纹理坐标属性
        gl.bindBuffer(gl.ARRAY_BUFFER, this.texCoordBuffer);
        const texCoordLocation = gl.getAttribLocation(this.program, 'a_texCoord');
        gl.enableVertexAttribArray(texCoordLocation);
        gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 0, 0);

        // 设置纹理
        gl.activeTexture(gl.TEXTURE0);
        gl.bindTexture(gl.TEXTURE_2D, this.texture);
        gl.uniform1i(this.uniforms.image, 0);

        // 绘制
        gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);

        // 检查错误
        const error = gl.getError();
        if (error !== gl.NO_ERROR) {
          console.error('WebGL error during render:', error);
        }
      } catch (e) {
        console.error('Error during render:', e);
      }
    }
  }
};
</script>

<style scoped>
.webgl-processor {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  border-radius: 4px;
  overflow: hidden;
}

canvas {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style> 