// 高级EXR Worker - 全流程处理，支持并行处理和缩略图生成

let exrModule = null;
let isInitialized = false;

// 加载EXR模块
async function loadExrModule() {
  if (exrModule) return exrModule;

  try {
    // 在Worker中加载WASM模块
    const response = await fetch('/wasm/exr_bridge.js');
    const scriptContent = await response.text();

    // 执行脚本
    eval(scriptContent);

    if (self.ExrModule) {
      exrModule = await self.ExrModule();
      isInitialized = true;
      return exrModule;
    } else {
      throw new Error('ExrModule not found after loading');
    }
  } catch (error) {
    throw new Error(`Failed to load EXR module: ${error.message}`);
  }
}

// Worker消息处理
self.onmessage = async function(e) {
  const { type, data, id, workerId } = e.data;

  try {
    switch (type) {
      case 'INIT_WORKER':
        await initializeWorker();
        self.postMessage({
          type: 'WORKER_INITIALIZED',
          id,
          workerId,
          data: { success: true }
        });
        break;

      case 'PROCESS_EXR_FULL':
        const result = await processExrFull(data);
        self.postMessage({
          type: 'EXR_FULL_RESULT',
          id,
          workerId,
          data: result
        });
        break;

      case 'PROCESS_LIGHT_GROUP':
        const groupResult = await processLightGroup(data);
        self.postMessage({
          type: 'LIGHT_GROUP_RESULT',
          id,
          workerId,
          data: groupResult
        });
        break;

      case 'GENERATE_THUMBNAIL':
        const thumbnailResult = await generateThumbnail(data);
        self.postMessage({
          type: 'THUMBNAIL_RESULT',
          id,
          workerId,
          data: thumbnailResult
        });
        break;

      case 'GENERATE_PREVIEW':
        const previewResult = await generatePreview(data);
        self.postMessage({
          type: 'PREVIEW_RESULT',
          id,
          workerId,
          data: previewResult
        });
        break;

      default:
        self.postMessage({
          type: 'ERROR',
          id,
          workerId,
          data: { error: 'Unknown message type: ' + type }
        });
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      id,
      workerId,
      data: { error: error.message }
    });
  }
};

// 初始化Worker
async function initializeWorker() {
  if (!isInitialized) {
    await loadExrModule();
  }
}

// 完整EXR文件处理
async function processExrFull(data) {
  const { arrayBuffer } = data;

  if (!exrModule) {
    throw new Error('EXR module not initialized');
  }

  const tempFileName = `worker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.exr`;

  try {
    // 创建临时文件
    exrModule.FS_createDataFile('/', tempFileName, new Uint8Array(arrayBuffer), true, true);

    // 获取基本信息
    const width = exrModule.get_exr_width(tempFileName);
    const height = exrModule.get_exr_height(tempFileName);
    const channels = exrModule.get_exr_channels(tempFileName);

    // 转换通道数据
    const jsChannels = convertChannelsToArray(channels);

    // 分析灯光组
    const lightGroups = analyzeLightGroups(jsChannels);

    // 清理临时文件
    exrModule.FS_unlink('/' + tempFileName);

    return {
      success: true,
      width,
      height,
      lightGroups,
      allChannels: jsChannels,
      totalGroups: lightGroups.length
    };

  } catch (error) {
    // 确保清理临时文件
    try {
      exrModule.FS_unlink('/' + tempFileName);
    } catch (cleanupError) {
      // 忽略清理错误
    }
    throw error;
  }
}

// 处理单个灯光组
async function processLightGroup(data) {
  const { arrayBuffer, groupName, channels, width, height, generateLowRes = false } = data;

  if (!exrModule) {
    throw new Error('EXR module not initialized');
  }

  const tempFileName = `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.exr`;

  try {
    // 创建临时文件
    exrModule.FS_createDataFile('/', tempFileName, new Uint8Array(arrayBuffer), true, true);

    // 提取RGB通道
    const rChannelName = channels.find(ch => ch.endsWith('.R'));
    const gChannelName = channels.find(ch => ch.endsWith('.G'));
    const bChannelName = channels.find(ch => ch.endsWith('.B'));

    if (!rChannelName || !gChannelName || !bChannelName) {
      throw new Error(`Incomplete RGB channels for group ${groupName}`);
    }

    // 获取通道数据
    const rData = extractChannelData(exrModule.get_exr_channel(tempFileName, rChannelName));
    const gData = extractChannelData(exrModule.get_exr_channel(tempFileName, gChannelName));
    const bData = extractChannelData(exrModule.get_exr_channel(tempFileName, bChannelName));

    // 合并RGB数据
    const textureData = combineRGBChannels(rData, gData, bData, width, height);

    let lowResData = null;
    if (generateLowRes) {
      // 生成低分辨率版本（1/4尺寸）
      lowResData = generateLowResolution(textureData, width, height, 4);
    }

    // 清理临时文件
    exrModule.FS_unlink('/' + tempFileName);

    return {
      success: true,
      groupName,
      textureData: textureData.buffer, // 传输ArrayBuffer
      width,
      height,
      lowResData: lowResData ? {
        data: lowResData.data.buffer,
        width: lowResData.width,
        height: lowResData.height
      } : null,
      channels: {
        r: rChannelName,
        g: gChannelName,
        b: bChannelName
      }
    };

  } catch (error) {
    // 确保清理临时文件
    try {
      exrModule.FS_unlink('/' + tempFileName);
    } catch (cleanupError) {
      // 忽略清理错误
    }
    throw error;
  }
}

// 生成缩略图
async function generateThumbnail(data) {
  const { arrayBuffer, maxSize = 256 } = data;

  if (!exrModule) {
    throw new Error('EXR module not initialized');
  }

  const tempFileName = `thumb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.exr`;

  try {
    // 创建临时文件
    exrModule.FS_createDataFile('/', tempFileName, new Uint8Array(arrayBuffer), true, true);

    // 获取基本信息
    const width = exrModule.get_exr_width(tempFileName);
    const height = exrModule.get_exr_height(tempFileName);

    // 计算缩略图尺寸
    const scale = Math.min(maxSize / width, maxSize / height);
    const thumbWidth = Math.floor(width * scale);
    const thumbHeight = Math.floor(height * scale);

    // 查找合适的通道用于缩略图（优先使用effectsResult或第一个RGB组）
    const channels = exrModule.get_exr_channels(tempFileName);
    const jsChannels = convertChannelsToArray(channels);

    let thumbnailData;

    // 尝试使用effectsResult通道
    const effectsResult = jsChannels.find(ch => ch.toLowerCase().includes('effectsresult'));
    if (effectsResult) {
      thumbnailData = await generateThumbnailFromSingleChannel(tempFileName, effectsResult, thumbWidth, thumbHeight);
    } else {
      // 使用第一个灯光组
      const lightGroups = analyzeLightGroups(jsChannels);
      if (lightGroups.length > 0) {
        thumbnailData = await generateThumbnailFromLightGroup(tempFileName, lightGroups[0], thumbWidth, thumbHeight);
      } else {
        throw new Error('No suitable channels found for thumbnail generation');
      }
    }

    // 清理临时文件
    exrModule.FS_unlink('/' + tempFileName);

    return {
      success: true,
      thumbnailData: thumbnailData.buffer,
      width: thumbWidth,
      height: thumbHeight,
      originalWidth: width,
      originalHeight: height
    };

  } catch (error) {
    // 确保清理临时文件
    try {
      exrModule.FS_unlink('/' + tempFileName);
    } catch (cleanupError) {
      // 忽略清理错误
    }
    throw error;
  }
}

// 生成预览图像
async function generatePreview(data) {
  const { arrayBuffer, previewWidth = 512, previewHeight = 512 } = data;

  if (!exrModule) {
    throw new Error('EXR module not initialized');
  }

  const tempFileName = `preview_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.exr`;

  try {
    // 创建临时文件
    exrModule.FS_createDataFile('/', tempFileName, new Uint8Array(arrayBuffer), true, true);

    // 获取基本信息
    const width = exrModule.get_exr_width(tempFileName);
    const height = exrModule.get_exr_height(tempFileName);

    // 计算预览尺寸（保持宽高比）
    const scale = Math.min(previewWidth / width, previewHeight / height);
    const scaledWidth = Math.floor(width * scale);
    const scaledHeight = Math.floor(height * scale);

    // 获取所有灯光组的预览数据
    const channels = exrModule.get_exr_channels(tempFileName);
    const jsChannels = convertChannelsToArray(channels);
    const lightGroups = analyzeLightGroups(jsChannels);

    const previewGroups = [];

    for (const group of lightGroups) {
      try {
        const groupPreview = await generateLightGroupPreview(tempFileName, group, scaledWidth, scaledHeight);
        previewGroups.push({
          name: group.name,
          data: groupPreview.buffer,
          width: scaledWidth,
          height: scaledHeight
        });
      } catch (groupError) {
        console.warn(`Failed to generate preview for group ${group.name}:`, groupError);
      }
    }

    // 清理临时文件
    exrModule.FS_unlink('/' + tempFileName);

    return {
      success: true,
      previewGroups,
      width: scaledWidth,
      height: scaledHeight,
      originalWidth: width,
      originalHeight: height,
      totalGroups: lightGroups.length
    };

  } catch (error) {
    // 确保清理临时文件
    try {
      exrModule.FS_unlink('/' + tempFileName);
    } catch (cleanupError) {
      // 忽略清理错误
    }
    throw error;
  }
}

// 工具函数：转换通道数据为数组
function convertChannelsToArray(channels) {
  const jsChannels = [];

  if (channels) {
    if (typeof channels.toArray === 'function') {
      return channels.toArray();
    } else if (typeof channels.size === 'function' && typeof channels.get === 'function') {
      const n = channels.size();
      for (let i = 0; i < n; i++) {
        jsChannels.push(channels.get(i));
      }
    } else if (typeof channels.length === 'number' && typeof channels.get === 'function') {
      for (let i = 0; i < channels.length; i++) {
        jsChannels.push(channels.get(i));
      }
    } else if (typeof channels === 'object') {
      for (let key in channels) {
        if (channels.hasOwnProperty(key) && !isNaN(Number(key))) {
          jsChannels.push(channels[key]);
        }
      }
    }
  }

  return jsChannels;
}

// 工具函数：分析灯光组
function analyzeLightGroups(jsChannels) {
  const channelMap = {};

  // 按组分类通道
  for (const ch of jsChannels) {
    const match = ch.match(/^(.*)\.[RGB]$/i);
    const group = match ? match[1] : ch;
    if (!channelMap[group]) channelMap[group] = [];
    channelMap[group].push(ch);
  }

  // 识别灯光组
  const lightGroups = [];
  for (const group in channelMap) {
    if (/light|Light|VRayLight|layer|Layer|AOV|aov/.test(group)) {
      const channelsInGroup = channelMap[group];
      const hasR = channelsInGroup.some(ch => ch.endsWith('.R'));
      const hasG = channelsInGroup.some(ch => ch.endsWith('.G'));
      const hasB = channelsInGroup.some(ch => ch.endsWith('.B'));

      if (hasR && hasG && hasB) {
        lightGroups.push({
          name: group,
          channels: channelsInGroup
        });
      }
    }
  }

  return lightGroups;
}

// 工具函数：提取通道数据
function extractChannelData(channelData) {
  if (!channelData) {
    throw new Error('Channel data is null or undefined');
  }

  if (typeof channelData.size === 'function' && typeof channelData.get === 'function') {
    const n = channelData.size();
    const floatArr = new Float32Array(n);
    for (let i = 0; i < n; i++) {
      floatArr[i] = channelData.get(i);
    }
    return floatArr;
  } else if (typeof channelData.length === 'number' && typeof channelData.get === 'function') {
    const floatArr = new Float32Array(channelData.length);
    for (let i = 0; i < channelData.length; i++) {
      floatArr[i] = channelData.get(i);
    }
    return floatArr;
  } else if (channelData instanceof Float32Array) {
    return channelData;
  } else if (typeof channelData.toArray === 'function') {
    return new Float32Array(channelData.toArray());
  } else if (Array.isArray(channelData)) {
    return new Float32Array(channelData);
  } else if (channelData && typeof channelData === 'object' && channelData.buffer && channelData.byteLength) {
    return new Float32Array(channelData);
  }

  throw new Error('Unable to extract channel data');
}

// 工具函数：合并RGB通道
function combineRGBChannels(rData, gData, bData, width, height) {
  const numPixels = width * height;
  const textureData = new Float32Array(numPixels * 3);

  if (rData.length !== numPixels || gData.length !== numPixels || bData.length !== numPixels) {
    throw new Error(`Channel data length mismatch: expected ${numPixels}, got R:${rData.length}, G:${gData.length}, B:${bData.length}`);
  }

  for (let i = 0; i < numPixels; i++) {
    textureData[i * 3] = rData[i] || 0;
    textureData[i * 3 + 1] = gData[i] || 0;
    textureData[i * 3 + 2] = bData[i] || 0;
  }

  return textureData;
}
// 工具函数：生成低分辨率版本
function generateLowResolution(textureData, width, height, downscaleFactor) {
  const newWidth = Math.floor(width / downscaleFactor);
  const newHeight = Math.floor(height / downscaleFactor);
  const newData = new Float32Array(newWidth * newHeight * 3);

  for (let y = 0; y < newHeight; y++) {
    for (let x = 0; x < newWidth; x++) {
      const srcX = x * downscaleFactor;
      const srcY = y * downscaleFactor;
      const srcIndex = (srcY * width + srcX) * 3;
      const dstIndex = (y * newWidth + x) * 3;

      // 简单采样（可以改为平均采样以获得更好质量）
      newData[dstIndex] = textureData[srcIndex];
      newData[dstIndex + 1] = textureData[srcIndex + 1];
      newData[dstIndex + 2] = textureData[srcIndex + 2];
    }
  }

  return {
    data: newData,
    width: newWidth,
    height: newHeight
  };
}

// 工具函数：从单通道生成缩略图
async function generateThumbnailFromSingleChannel(tempFileName, channelName, thumbWidth, thumbHeight) {
  const channelData = exrModule.get_exr_channel(tempFileName, channelName);
  const floatData = extractChannelData(channelData);

  // 假设是灰度数据，转换为RGB
  const thumbnailData = new Float32Array(thumbWidth * thumbHeight * 3);

  // 简单缩放（最近邻）
  const originalWidth = exrModule.get_exr_width(tempFileName);
  const originalHeight = exrModule.get_exr_height(tempFileName);

  for (let y = 0; y < thumbHeight; y++) {
    for (let x = 0; x < thumbWidth; x++) {
      const srcX = Math.floor((x / thumbWidth) * originalWidth);
      const srcY = Math.floor((y / thumbHeight) * originalHeight);
      const srcIndex = srcY * originalWidth + srcX;
      const dstIndex = (y * thumbWidth + x) * 3;

      const value = floatData[srcIndex] || 0;
      thumbnailData[dstIndex] = value;     // R
      thumbnailData[dstIndex + 1] = value; // G
      thumbnailData[dstIndex + 2] = value; // B
    }
  }

  return thumbnailData;
}

// 工具函数：从灯光组生成缩略图
async function generateThumbnailFromLightGroup(tempFileName, lightGroup, thumbWidth, thumbHeight) {
  const rChannelName = lightGroup.channels.find(ch => ch.endsWith('.R'));
  const gChannelName = lightGroup.channels.find(ch => ch.endsWith('.G'));
  const bChannelName = lightGroup.channels.find(ch => ch.endsWith('.B'));

  const rData = extractChannelData(exrModule.get_exr_channel(tempFileName, rChannelName));
  const gData = extractChannelData(exrModule.get_exr_channel(tempFileName, gChannelName));
  const bData = extractChannelData(exrModule.get_exr_channel(tempFileName, bChannelName));

  const originalWidth = exrModule.get_exr_width(tempFileName);
  const originalHeight = exrModule.get_exr_height(tempFileName);

  const thumbnailData = new Float32Array(thumbWidth * thumbHeight * 3);

  // 简单缩放（最近邻）
  for (let y = 0; y < thumbHeight; y++) {
    for (let x = 0; x < thumbWidth; x++) {
      const srcX = Math.floor((x / thumbWidth) * originalWidth);
      const srcY = Math.floor((y / thumbHeight) * originalHeight);
      const srcIndex = srcY * originalWidth + srcX;
      const dstIndex = (y * thumbWidth + x) * 3;

      thumbnailData[dstIndex] = rData[srcIndex] || 0;     // R
      thumbnailData[dstIndex + 1] = gData[srcIndex] || 0; // G
      thumbnailData[dstIndex + 2] = bData[srcIndex] || 0; // B
    }
  }

  return thumbnailData;
}

// 工具函数：生成灯光组预览
async function generateLightGroupPreview(tempFileName, lightGroup, previewWidth, previewHeight) {
  const rChannelName = lightGroup.channels.find(ch => ch.endsWith('.R'));
  const gChannelName = lightGroup.channels.find(ch => ch.endsWith('.G'));
  const bChannelName = lightGroup.channels.find(ch => ch.endsWith('.B'));

  const rData = extractChannelData(exrModule.get_exr_channel(tempFileName, rChannelName));
  const gData = extractChannelData(exrModule.get_exr_channel(tempFileName, gChannelName));
  const bData = extractChannelData(exrModule.get_exr_channel(tempFileName, bChannelName));

  const originalWidth = exrModule.get_exr_width(tempFileName);
  const originalHeight = exrModule.get_exr_height(tempFileName);

  const previewData = new Float32Array(previewWidth * previewHeight * 3);

  // 双线性插值缩放（更好的质量）
  for (let y = 0; y < previewHeight; y++) {
    for (let x = 0; x < previewWidth; x++) {
      const srcX = (x / previewWidth) * originalWidth;
      const srcY = (y / previewHeight) * originalHeight;

      const x1 = Math.floor(srcX);
      const y1 = Math.floor(srcY);
      const x2 = Math.min(x1 + 1, originalWidth - 1);
      const y2 = Math.min(y1 + 1, originalHeight - 1);

      const fx = srcX - x1;
      const fy = srcY - y1;

      const dstIndex = (y * previewWidth + x) * 3;

      // 双线性插值
      for (let c = 0; c < 3; c++) {
        const data = c === 0 ? rData : (c === 1 ? gData : bData);

        const v1 = data[y1 * originalWidth + x1] || 0;
        const v2 = data[y1 * originalWidth + x2] || 0;
        const v3 = data[y2 * originalWidth + x1] || 0;
        const v4 = data[y2 * originalWidth + x2] || 0;

        const i1 = v1 * (1 - fx) + v2 * fx;
        const i2 = v3 * (1 - fx) + v4 * fx;
        const value = i1 * (1 - fy) + i2 * fy;

        previewData[dstIndex + c] = value;
      }
    }
  }

  return previewData;
}

// Worker初始化完成
self.postMessage({
  type: 'WORKER_READY'
});
