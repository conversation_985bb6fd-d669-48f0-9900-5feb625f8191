<template>
  <div class="image-editor">
    <div class="sidebar">
      <div class="sidebar-header">
        <h3>图片编辑器</h3>
      </div>
      <div class="controls">
        <div class="control-group">
          <label>对比度</label>
          <div class="slider-container">
            <input type="range" min="0" max="200" v-model.number="contrast" />
            <input type="number" v-model.number="contrast" style="width: 80px; text-align: center;" />
          </div>
        </div>
        <div class="control-group">
          <label>亮度</label>
          <div class="slider-container">
            <input type="range" min="0" max="200" v-model.number="brightness" />
            <input type="number" v-model.number="brightness" style="width: 80px; text-align: center;" />
          </div>
        </div>
        <div class="control-group">
          <label>色相</label>
          <div class="slider-container">
            <input type="range" min="0" max="360" v-model.number="hue" />
            <input type="number" v-model.number="hue" style="width: 80px; text-align: center;" />
          </div>
        </div>
        <div class="control-group">
          <label>饱和度</label>
          <div class="slider-container">
            <input type="range" min="0" max="200" v-model.number="saturation" />
            <input type="number" v-model.number="saturation" style="width: 80px; text-align: center;" />
          </div>
        </div>
        <div class="control-group">
          <label>锐化</label>
          <div class="slider-container">
            <input type="range" min="0" max="1" step="0.1" v-model.number="sharpen" />
            <input type="number" v-model.number="sharpen" step="0.1" style="width: 80px; text-align: center;" />
          </div>
        </div>
      </div>
    </div>

    <div class="preview">
      <div class="preview-header">
        <h3>实时预览</h3>
      </div>
      <div class="preview-content">
        <WebGLImageProcessor
          v-if="loadedImage"
          :image="loadedImage"
          :contrast="contrast"
          :brightness="brightness"
          :hue="hue"
          :saturation="saturation"
          :sharpen="sharpen"
        />
      </div>
    </div>
  </div>
</template>

<script>
import imageSrc from '../123.png';
import WebGLImageProcessor from './WebGLImageProcessor.vue';

export default {
  components: {
    WebGLImageProcessor
  },
  data() {
    return {
      contrast: 100,
      brightness: 100,
      hue: 0,
      saturation: 100,
      sharpen: 0,
      loadedImage: null
    };
  },
  mounted() {
    // 加载图片
    const img = new Image();
    img.src = imageSrc;
    img.onload = () => {
      this.loadedImage = img;
    };
    img.onerror = (e) => {
      console.error("图片加载失败:", e);
    };
  }
};
</script>

<style scoped>
.image-editor {
  display: flex;
  flex-direction: row;
  gap: 20px;
  height: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.sidebar {
  width: 360px;
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.sidebar-header h3 {
  margin: 0;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

.controls {
  padding: 16px;
  overflow-y: auto;
}

.control-group {
  margin-bottom: 20px;
}

.control-group label {
  display: block;
  margin-bottom: 8px;
  color: #475569;
  font-size: 14px;
  font-weight: 500;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-container input[type="range"] {
  flex: 1;
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  outline: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider-container input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  background: #2563eb;
}

.slider-container input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.slider-container input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.1);
  background: #2563eb;
}

.slider-container input[type="number"] {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  color: #475569;
  text-align: center;
  background-color: #ffffff;
}

.slider-container input[type="number"]:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.preview-header {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.preview-header h3 {
  margin: 0;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

.preview-content {
  flex: 1;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  border-radius: 4px;
  overflow: hidden;
}
</style>
