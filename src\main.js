import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import { createRouter, createWebHistory } from 'vue-router';
import ImageEditor from './components/ImageEditor.vue';
import ExrEditor from './components/ExrEditor.vue';

const routes = [
  { path: '/', redirect: '/image' },
  { path: '/image', component: ImageEditor },
  { path: '/exr', component: ExrEditor },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

const app = createApp(App);
app.use(router);
app.mount('#app')
