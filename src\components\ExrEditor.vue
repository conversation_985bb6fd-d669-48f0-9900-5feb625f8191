<template>
  <div class="exr-editor">
    <div class="sidebar">
      <h3>灯光组</h3>
      <div v-for="group in Object.keys(groupToChannels)" :key="group" style="margin-bottom: 16px;">
        <label style="display: block; margin-bottom: 4px; text-align: left;">
          <input type="checkbox" v-model="selectedChannels" :value="group" @change="updatePreview" style="margin-right: 6px; vertical-align: middle;" />
          <span style="vertical-align: middle;">{{ group }}</span>
        </label>
        <div style="display: flex; align-items: center; gap: 8px;">
          <span style="width: 40px;">强度</span>
          <input type="range" min="0" max="10" step="0.1" v-model="groupScales[group]" @input="updatePreview" style="flex:1; width: 100%; vertical-align: middle;" />
          <input type="number" v-model.number="groupScales[group]" @input="updatePreview" @keyup.enter="updatePreview" style="width: 80px; text-align: center;" oninput="this.style.webkitAppearance='none';">
        </div>
        <div style="display: flex; align-items: center; gap: 8px;">
          <span style="width: 40px;">色温</span>
          <input type="range" min="1000" max="15000" step="100" v-model="groupColorTemperatures[group]" @input="updatePreview" style="flex:1; width: 100%; vertical-align: middle;" />
          <input type="number" v-model.number="groupColorTemperatures[group]" @input="updatePreview" @keyup.enter="updatePreview" style="width: 80px; text-align: center;" min="1000" max="15000">
        </div>
        <div style="display: flex; align-items: center; gap: 4px; margin-top: 4px;">
          <span style="width: 40px; font-size: 12px;">预设</span>
          <select v-model="groupColorTemperatures[group]" @change="updatePreview" style="flex: 1; padding: 2px; font-size: 12px;">
            <option v-for="preset in colorTempPresets" :key="preset.name" :value="preset.temp">
              {{ preset.name }} ({{ preset.temp }}K)
            </option>
          </select>
        </div>
      </div>
      <input type="file" accept=".exr" @change="onFileChange" />

      <!-- 渐进式加载控制 -->
      <div v-if="Object.keys(lightTextures).length > 0" class="progressive-controls">
        <h4>渲染质量</h4>
        <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
          <input type="checkbox" v-model="progressiveLoadingEnabled" @change="updatePreview" />
          <span>启用渐进式加载</span>
        </label>
        <label style="display: flex; align-items: center; gap: 8px;">
          <input type="checkbox" v-model="useHighResTextures" @change="switchTextureResolution" />
          <span>使用高分辨率纹理</span>
        </label>
      </div>

      <!-- 缓存状态显示 -->
      <div v-if="!isLoading && Object.keys(lightTextures).length > 0" class="cache-info">
        <h4>缓存状态</h4>
        <p>已缓存: {{ textureCache.getStats().totalEntries }} 个纹理</p>
        <p>内存使用: {{ (textureCache.getStats().currentSize / 1024 / 1024).toFixed(1) }}MB / {{ (textureCache.getStats().maxSize / 1024 / 1024).toFixed(0) }}MB</p>
        <div class="cache-bar">
          <div class="cache-fill" :style="{ width: textureCache.getStats().usagePercent + '%' }"></div>
        </div>
      </div>
    </div>
    <div class="preview">
      <h3>实时预览 (GPU 加速)</h3>
      <div class="canvas-container">
        <canvas ref="canvas"></canvas>

        <!-- 未加载时的提示 -->
        <div v-if="!isLoading && Object.keys(lightTextures).length === 0" class="empty-state">
          <p>请选择一个EXR文件开始编辑</p>
        </div>

        <!-- 加载进度指示器 - 覆盖在canvas上层 -->
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-indicator">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: loadingProgress + '%' }"></div>
            </div>
            <div class="progress-info">
              <span class="progress-percent">{{ Math.round(loadingProgress) }}%</span>
              <span class="progress-time">{{ formatElapsedTime(loadingElapsedTime) }}</span>
            </div>
            <p class="progress-text">{{ processingStage || '正在加载 EXR 文件...' }}</p>
            <div v-if="thumbnailReady || previewReady" class="progress-features">
              <span v-if="thumbnailReady" class="feature-ready">✓ 缩略图</span>
              <span v-if="previewReady" class="feature-ready">✓ 预览</span>
              <span v-if="progressiveLoadingEnabled" class="feature-enabled">渐进式加载</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import vertexShaderSource from '../shaders/vertexShader.vert?raw';
import fragmentShaderSource from '../shaders/lightChannelShader.frag?raw';
import { globalTextureCache } from '../utils/TextureCache.js';
import { getColorTempPresets } from '../utils/ColorTemperature.js';
import { globalExrProcessor } from '../utils/ExrProcessorManager.js';
import { globalWorkerPool } from '../utils/WorkerPool.js';

// 旧的WASM加载代码已移至Worker中处理

export default {
  data() {
    return {
      gl: null,
      shaderProgram: null,
      positionBuffer: null,
      texCoordBuffer: null,
      indexBuffer: null,
      lightTextures: {},
      lowResTextures: {}, // 低分辨率纹理缓存
      attributeLocations: {},
      uniformLocations: {},
      exrObj: null,
      width: 0,
      height: 0,
      groupToChannels: {},
      selectedChannels: [],
      groupScales: {},
      groupColorTemperatures: {},
      isLoading: false,
      loadingProgress: 0,
      loadingStartTime: 0,
      loadingElapsedTime: 0,
      loadingTimer: null,
      renderDebounceTimer: null,
      textureCache: globalTextureCache,
      exrProcessor: globalExrProcessor,
      workerPool: globalWorkerPool,
      colorTempPresets: getColorTempPresets(),
      // 新增状态
      processingStage: '',
      thumbnailReady: false,
      previewReady: false,
      useHighResTextures: false, // 是否使用高分辨率纹理
      progressiveLoadingEnabled: true, // 是否启用渐进式加载
    };
  },
  async mounted() {
    await this.initializeWorkerSystem();
    this.setupWebGL();
    this.setupExrProcessorCallbacks();
    window.addEventListener('resize', this.handleResize);
  },
  methods: {
    // 初始化Worker系统
    async initializeWorkerSystem() {
      try {
        console.log('Initializing worker pool...');
        await this.workerPool.initialize();
        console.log('Worker pool initialized successfully');
      } catch (error) {
        console.error('Failed to initialize worker pool:', error);
        // 可以选择回退到主线程处理
      }
    },

    // 设置WebGL
    setupWebGL() {
      const canvas = this.$refs.canvas;
      this.gl = canvas.getContext('webgl2');
      if (!this.gl) {
        alert('无法初始化 WebGL2，您的浏览器可能不支持。请尝试支持 WebGL2 的浏览器。');
        return;
      }
      this.textureCache.setGL(this.gl);

      const vertexShader = this.compileShader(this.gl, this.gl.VERTEX_SHADER, vertexShaderSource);
      const fragmentShader = this.compileShader(this.gl, this.gl.FRAGMENT_SHADER, fragmentShaderSource);
      if (!vertexShader || !fragmentShader) return;

      this.shaderProgram = this.initShaderProgram(this.gl, vertexShader, fragmentShader);
      if (!this.shaderProgram) return;

      this.gl.useProgram(this.shaderProgram);
      this.attributeLocations = {
        position: this.gl.getAttribLocation(this.shaderProgram, 'a_position'),
        texCoord: this.gl.getAttribLocation(this.shaderProgram, 'a_texCoord'),
      };
      this.uniformLocations = {
        lightTextures: this.gl.getUniformLocation(this.shaderProgram, 'u_lightTextures'),
        lightScales: this.gl.getUniformLocation(this.shaderProgram, 'u_lightScales'),
        lightColorTemps: this.gl.getUniformLocation(this.shaderProgram, 'u_lightColorTemps'),
        numLights: this.gl.getUniformLocation(this.shaderProgram, 'u_numLights'),
      };

      // 创建几何缓冲区
      const positions = new Float32Array([-1.0, 1.0, 1.0, 1.0, -1.0, -1.0, 1.0, -1.0]);
      this.positionBuffer = this.gl.createBuffer();
      this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.positionBuffer);
      this.gl.bufferData(this.gl.ARRAY_BUFFER, positions, this.gl.STATIC_DRAW);

      const texCoords = new Float32Array([0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]);
      this.texCoordBuffer = this.gl.createBuffer();
      this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.texCoordBuffer);
      this.gl.bufferData(this.gl.ARRAY_BUFFER, texCoords, this.gl.STATIC_DRAW);

      const indices = new Uint16Array([0, 1, 2, 1, 3, 2]);
      this.indexBuffer = this.gl.createBuffer();
      this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
      this.gl.bufferData(this.gl.ELEMENT_ARRAY_BUFFER, indices, this.gl.STATIC_DRAW);

      this.gl.bindBuffer(this.gl.ARRAY_BUFFER, null);
      this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, null);
    },

    // 设置EXR处理器回调
    setupExrProcessorCallbacks() {
      this.exrProcessor.setCallbacks({
        onProgress: (state) => {
          this.loadingProgress = state.progress;
          this.processingStage = state.stage;
        },
        onStageChange: (stage) => {
          this.processingStage = stage;
          console.log(`Processing stage: ${stage}`);
        },
        onThumbnailReady: (thumbnailData) => {
          this.thumbnailReady = true;
          console.log('Thumbnail ready:', thumbnailData.width, 'x', thumbnailData.height);
          // 可以在这里显示缩略图
        },
        onPreviewReady: (previewData) => {
          this.previewReady = true;
          console.log('Preview ready:', previewData.groups.length, 'groups');
          // 创建预览纹理并显示
          this.createPreviewTextures(previewData);
        },
        onGroupReady: (groupData) => {
          console.log(`Light group ready: ${groupData.groupName}`);
          this.createLightTexture(groupData);
        },
        onComplete: (result) => {
          console.log('EXR processing complete:', result);
          this.stopLoadingTimer();
          this.$nextTick(() => {
            this.adjustCanvasDisplaySize();
            this.updatePreview();
          });
        },
        onError: (error) => {
          console.error('EXR processing error:', error);
          this.stopLoadingTimer();
          alert(`文件处理失败: ${error.message}`);
        }
      });
    },

    // 创建预览纹理
    createPreviewTextures(previewData) {
      if (!this.progressiveLoadingEnabled) return;

      for (const group of previewData.groups) {
        const texture = this.createTextureFromData(group.data, group.width, group.height);
        if (texture) {
          // 存储为低分辨率纹理
          this.lowResTextures[group.name] = texture;

          // 如果还没有高分辨率纹理，先使用低分辨率
          if (!this.lightTextures[group.name]) {
            this.lightTextures[group.name] = texture;
          }
        }
      }

      // 更新组信息
      for (const group of previewData.groups) {
        if (!this.groupToChannels[group.name]) {
          this.groupToChannels[group.name] = []; // 临时设置，后续会被完整数据覆盖
        }
        if (this.groupScales[group.name] === undefined) {
          this.groupScales[group.name] = 1;
        }
        if (this.groupColorTemperatures[group.name] === undefined) {
          this.groupColorTemperatures[group.name] = 6500;
        }
      }

      // 更新选中的通道
      this.selectedChannels = previewData.groups.map(g => g.name);

      // 设置画布尺寸
      this.width = previewData.width;
      this.height = previewData.height;

      const canvas = this.$refs.canvas;
      canvas.width = this.width;
      canvas.height = this.height;

      if (this.gl) {
        this.gl.viewport(0, 0, this.width, this.height);
      }

      // 立即渲染预览
      this.$nextTick(() => {
        this.adjustCanvasDisplaySize();
        this.updatePreview();
      });
    },

    // 创建灯光纹理
    createLightTexture(groupData) {
      const { groupName, textureData, width, height, lowResData } = groupData;

      // 创建高分辨率纹理
      const highResTexture = this.createTextureFromData(textureData, width, height);
      if (highResTexture) {
        this.lightTextures[groupName] = highResTexture;

        // 更新画布尺寸（如果需要）
        if (!this.width || !this.height) {
          this.width = width;
          this.height = height;
          const canvas = this.$refs.canvas;
          canvas.width = this.width;
          canvas.height = this.height;
          if (this.gl) {
            this.gl.viewport(0, 0, this.width, this.height);
          }
        }
      }

      // 如果有低分辨率数据，也创建低分辨率纹理
      if (lowResData) {
        const lowResTexture = this.createTextureFromData(lowResData.data, lowResData.width, lowResData.height);
        if (lowResTexture) {
          this.lowResTextures[groupName] = lowResTexture;
        }
      }

      // 更新组信息
      if (!this.groupToChannels[groupName]) {
        this.groupToChannels[groupName] = []; // 临时设置
      }
      if (this.groupScales[groupName] === undefined) {
        this.groupScales[groupName] = 1;
      }
      if (this.groupColorTemperatures[groupName] === undefined) {
        this.groupColorTemperatures[groupName] = 6500;
      }

      // 添加到选中通道（如果还没有）
      if (!this.selectedChannels.includes(groupName)) {
        this.selectedChannels.push(groupName);
      }

      // 立即更新渲染
      this.$nextTick(() => {
        this.updatePreview();
      });
    },

    // 从数据创建WebGL纹理
    createTextureFromData(textureData, width, height) {
      if (!this.gl) {
        console.error("WebGL context is not available to create texture.");
        return null;
      }

      const texture = this.gl.createTexture();
      this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
      this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL, true);
      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.NEAREST);
      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.NEAREST);

      const internalFormat = this.gl.RGB32F;
      const format = this.gl.RGB;
      const type = this.gl.FLOAT;

      this.gl.texImage2D(this.gl.TEXTURE_2D, 0, internalFormat, width, height, 0, format, type, textureData);
      this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL, false);
      this.gl.bindTexture(this.gl.TEXTURE_2D, null);

      return texture;
    },

    // 切换纹理分辨率
    switchTextureResolution() {
      if (this.useHighResTextures) {
        // 切换到高分辨率纹理
        console.log('Switching to high resolution textures');
        // 高分辨率纹理已经在lightTextures中，无需额外操作
      } else {
        // 切换到低分辨率纹理
        console.log('Switching to low resolution textures');
        for (const groupName in this.lowResTextures) {
          if (this.lowResTextures[groupName]) {
            this.lightTextures[groupName] = this.lowResTextures[groupName];
          }
        }
      }

      // 立即更新渲染
      this.$nextTick(() => {
        this.updatePreview();
      });
    },
    // 格式化加载时间显示
    formatElapsedTime(seconds) {
      if (seconds < 60) {
        return `${seconds.toFixed(1)}s`;
      } else {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = (seconds % 60).toFixed(1);
        return `${minutes}m ${remainingSeconds}s`;
      }
    },
    // 开始加载计时
    startLoadingTimer() {
      this.isLoading = true;
      this.loadingProgress = 0;
      this.loadingStartTime = Date.now();
      this.loadingElapsedTime = 0;

      // 每100ms更新一次时间显示
      this.loadingTimer = setInterval(() => {
        this.loadingElapsedTime = (Date.now() - this.loadingStartTime) / 1000;
      }, 100);
    },
    // 停止加载计时
    stopLoadingTimer() {
      this.isLoading = false;
      if (this.loadingTimer) {
        clearInterval(this.loadingTimer);
        this.loadingTimer = null;
      }
      console.log(`Loading completed in ${this.formatElapsedTime(this.loadingElapsedTime)}`);
    },
    // 调整canvas显示尺寸以适应容器并保持宽高比
    adjustCanvasDisplaySize() {
      const canvas = this.$refs.canvas;
      const container = canvas.parentElement;

      if (!canvas || !container || !this.width || !this.height) {
        return;
      }

      // 获取容器的实际尺寸
      const containerRect = container.getBoundingClientRect();
      const containerWidth = containerRect.width;
      const containerHeight = containerRect.height;

      // 确保容器有有效尺寸
      if (containerWidth <= 0 || containerHeight <= 0) {
        console.warn('Container has invalid dimensions:', containerWidth, 'x', containerHeight);
        return;
      }

      // 限制容器尺寸，防止超过屏幕
      const maxContainerWidth = Math.min(containerWidth, window.innerWidth * 0.8);
      const maxContainerHeight = Math.min(containerHeight, window.innerHeight * 0.8);

      // 计算EXR文件的宽高比
      const aspectRatio = this.width / this.height;

      // 计算最佳显示尺寸，保持宽高比
      let displayWidth, displayHeight;

      // 使用更保守的边距，确保不溢出
      const marginRatio = 0.9; // 使用90%的容器空间，留10%边距

      if (maxContainerWidth / maxContainerHeight > aspectRatio) {
        // 容器比图片更宽，以高度为准
        displayHeight = maxContainerHeight * marginRatio;
        displayWidth = displayHeight * aspectRatio;

        // 如果计算出的宽度超出容器，则以宽度为准重新计算
        if (displayWidth > maxContainerWidth) {
          displayWidth = maxContainerWidth * marginRatio;
          displayHeight = displayWidth / aspectRatio;
        }
      } else {
        // 容器比图片更高，以宽度为准
        displayWidth = maxContainerWidth * marginRatio;
        displayHeight = displayWidth / aspectRatio;

        // 如果计算出的高度超出容器，则以高度为准重新计算
        if (displayHeight > maxContainerHeight) {
          displayHeight = maxContainerHeight * marginRatio;
          displayWidth = displayHeight * aspectRatio;
        }
      }

      // 设置canvas的显示尺寸（CSS尺寸）
      canvas.style.width = `${displayWidth}px`;
      canvas.style.height = `${displayHeight}px`;
      canvas.style.display = 'block'; 
    },
    // 窗口大小改变时重新调整canvas尺寸
    handleResize() {
      if (this.width && this.height) {
        this.adjustCanvasDisplaySize();
      }
    },
    compileShader(gl, type, source) {
      const shader = gl.createShader(type);
      gl.shaderSource(shader, source);
      gl.compileShader(shader);
      if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('编译着色器时出错: ' + gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        return null;
      }
      return shader;
    },
    initShaderProgram(gl, vs, fs) {
      const shaderProgram = gl.createProgram();
      gl.attachShader(shaderProgram, vs);
      gl.attachShader(shaderProgram, fs);
      gl.linkProgram(shaderProgram);
      if (!gl.getProgramParameter(shaderProgram, gl.LINK_STATUS)) {
        console.error('无法初始化着色器程序: ' + gl.getProgramInfoLog(shaderProgram));
        return null;
      }
      return shaderProgram;
    },
    async onFileChange(e) {
      const file = e.target.files[0];
      if (!file) return;

      // 验证文件格式
      if (!file.name.toLowerCase().endsWith('.exr')) {
        alert('请选择有效的EXR文件（.exr格式）');
        return;
      }

      console.log(`Loading file: ${file.name}, size: ${(file.size / 1024 / 1024).toFixed(2)}MB`);

      // 开始加载计时
      this.startLoadingTimer();

      // 清理现有资源
      this.cleanupResources();

      try {
        // 使用新的Worker架构处理文件
        await this.exrProcessor.processExrFile(file, {
          generateThumbnail: true,
          generatePreview: this.progressiveLoadingEnabled,
          thumbnailSize: 256,
          previewSize: 512,
          enableParallelProcessing: true,
          maxConcurrentGroups: 3
        });

      } catch (error) {
        console.error('File processing failed:', error);

        // 提供更详细的错误信息
        let errorMessage = '文件加载失败：';
        if (error.message.includes('Worker')) {
          errorMessage += 'Worker处理失败，请检查浏览器兼容性。';
        } else if (error.message.includes('format') || error.message.includes('EXR')) {
          errorMessage += '文件格式不正确，请确保是有效的EXR文件。';
        } else if (error.message.includes('memory') || error.message.includes('Memory')) {
          errorMessage += '内存不足，请尝试较小的文件或关闭其他应用程序。';
        } else {
          errorMessage += error.message || '未知错误，请检查文件格式。';
        }

        alert(errorMessage);
      }
    },

    // 清理现有资源
    cleanupResources() {
      this.textureCache.clear();

      // 清理WebGL纹理
      if (this.gl) {
        for (const texture of Object.values(this.lightTextures)) {
          if (texture) this.gl.deleteTexture(texture);
        }
        for (const texture of Object.values(this.lowResTextures)) {
          if (texture) this.gl.deleteTexture(texture);
        }
      }

      this.lightTextures = {};
      this.lowResTextures = {};
      this.groupScales = {};
      this.groupColorTemperatures = {};
      this.selectedChannels = [];
      this.width = 0;
      this.height = 0;
      this.groupToChannels = {};
      this.thumbnailReady = false;
      this.previewReady = false;
      this.useHighResTextures = false;

      // 清理EXR处理器状态
      this.exrProcessor.cleanup();
    },


    // 优化的防抖渲染方法
    updatePreview() {
      // 清除之前的防抖定时器
      if (this.renderDebounceTimer) {
        clearTimeout(this.renderDebounceTimer);
      }

      // 设置新的防抖定时器，16ms约等于60fps
      this.renderDebounceTimer = setTimeout(() => {
        this.performRender();
      }, 16);
    },

    performRender() {
      const gl = this.gl;
      if (!gl) {
        console.warn("WebGL context is not available in updatePreview.");
        return;
      }
      if (!this.shaderProgram || Object.keys(this.lightTextures).length === 0) {
        console.warn("Shader program not loaded, or no light textures loaded. Skipping render.");
        gl.clearColor(0.0, 0.0, 0.0, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);
        return;
      }
      gl.disable(gl.DEPTH_TEST);
      gl.disable(gl.CULL_FACE);
      gl.disable(gl.BLEND);
      gl.useProgram(this.shaderProgram);
      gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
      gl.vertexAttribPointer(this.attributeLocations.position, 2, gl.FLOAT, false, 0, 0);
      gl.enableVertexAttribArray(this.attributeLocations.position);
      gl.bindBuffer(gl.ARRAY_BUFFER, this.texCoordBuffer);
      gl.vertexAttribPointer(this.attributeLocations.texCoord, 2, gl.FLOAT, false, 0, 0);
      gl.enableVertexAttribArray(this.attributeLocations.texCoord);
      gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
      gl.clearColor(0.0, 0.0, 0.0, 1.0);
      gl.clear(gl.COLOR_BUFFER_BIT);
      let textureUnitIndex = 0;
      const activeLightTextures = [];
      const activeLightScales = [];
      const activeLightColorTemps = [];
      const MAX_SHADER_LIGHTS = 16;
      for (const group of this.selectedChannels) {
        const lightTexture = this.lightTextures[group];
        if (lightTexture && activeLightTextures.length < MAX_SHADER_LIGHTS) {
          gl.activeTexture(gl.TEXTURE0 + textureUnitIndex);
          gl.bindTexture(gl.TEXTURE_2D, lightTexture);
          activeLightTextures.push(textureUnitIndex);
          activeLightScales.push((this.groupScales[group] ?? 1));
          activeLightColorTemps.push((this.groupColorTemperatures[group] ?? 6500));
          textureUnitIndex++;
        } else if (lightTexture && activeLightTextures.length >= MAX_SHADER_LIGHTS) {
          console.warn(`超过片段着色器支持的最大灯光通道数量 (${MAX_SHADER_LIGHTS})，忽略组: ${group}`);
        } else {
          console.warn(`未找到灯光组 "${group}" 的合并纹理数据。`);
        }
      }
      if (this.uniformLocations.lightTextures) {
        gl.uniform1iv(this.uniformLocations.lightTextures, new Int32Array(activeLightTextures));
      } else {
        console.warn("lightTextures uniform location not found.");
      }
      if (this.uniformLocations.lightScales) {
        gl.uniform1fv(this.uniformLocations.lightScales, new Float32Array(activeLightScales));
      } else {
        console.warn("lightScales uniform location not found.");
      }
      if (this.uniformLocations.lightColorTemps) {
        gl.uniform1fv(this.uniformLocations.lightColorTemps, new Float32Array(activeLightColorTemps));
      } else {
        console.warn("lightColorTemps uniform location not found.");
      }
      if (this.uniformLocations.numLights) {
        gl.uniform1i(this.uniformLocations.numLights, activeLightTextures.length);
      } else {
        console.warn("numLights uniform location not found.");
      }
      gl.drawElements(gl.TRIANGLES, 6, gl.UNSIGNED_SHORT, 0);
    },

  },
  beforeUnmount() {
    // 清理定时器
    if (this.renderDebounceTimer) {
      clearTimeout(this.renderDebounceTimer);
    }
    if (this.loadingTimer) {
      clearInterval(this.loadingTimer);
    }

    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize);

    // 清理Worker池
    if (this.workerPool) {
      this.workerPool.terminate();
    }

    // 清理EXR处理器
    if (this.exrProcessor) {
      this.exrProcessor.cleanup();
    }

    // 清理WebGL资源
    if (this.gl) {
      this.textureCache.clear();

      // 清理纹理
      for (const texture of Object.values(this.lightTextures)) {
        if (texture) this.gl.deleteTexture(texture);
      }
      for (const texture of Object.values(this.lowResTextures)) {
        if (texture) this.gl.deleteTexture(texture);
      }

      // 清理缓冲区
      if (this.positionBuffer) this.gl.deleteBuffer(this.positionBuffer);
      if (this.texCoordBuffer) this.gl.deleteBuffer(this.texCoordBuffer);
      if (this.indexBuffer) this.gl.deleteBuffer(this.indexBuffer);

      // 清理着色器程序
      if (this.shaderProgram) this.gl.deleteProgram(this.shaderProgram);
    }
  }
};
</script>

<style scoped>
.exr-editor {
  display: flex;
  flex-direction: row;
  gap: 20px;
  height: 100%;
  max-height: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  box-sizing: border-box;
}

.sidebar {
  width: 360px;
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow-y: auto;
}

.sidebar h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

.preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  padding: 16px;
  min-height: 0; /* 允许flex子元素收缩 */
  overflow: hidden; /* 防止溢出 */
}

.preview h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

/* Canvas容器样式 */
.canvas-container {
  position: relative;
  width: 100%;
  flex: 1; /* 占据剩余空间 */
  min-height: 400px; /* 最小高度 */
  max-height: 100%; /* 限制最大高度，留出空间给其他元素 */
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8fafc;
  border-radius: 8px;
  overflow: hidden; /* 防止内容溢出 */
}

canvas {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: none; /* 初始隐藏，加载后显示 */
}

/* 空状态提示 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #64748b;
  font-size: 16px;
}

.empty-state p {
  margin: 0;
}

/* 加载覆盖层样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  outline: none;
  transition: all 0.2s ease;
}

input[type="range"]:hover {
  background: #cbd5e1;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  background: #2563eb;
}

input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.1);
  background: #2563eb;
}

input[type="number"] {
  width: 60px;
  padding: 4px 8px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  color: #475569;
  text-align: center;
  background-color: #ffffff;
}

input[type="number"]:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

input[type="file"] {
  margin-top: 16px;
  padding: 8px;
  border: 1px dashed #e2e8f0;
  border-radius: 4px;
  background-color: #ffffff;
  color: #475569;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="file"]:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

label {
  color: #475569;
  font-size: 14px;
  font-weight: 500;
}

span {
  color: #64748b;
  font-size: 14px;
}

/* 加载进度指示器样式 */
.loading-indicator {
  padding: 24px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  min-width: 320px;
  max-width: 400px;
}

.progress-bar {
  width: 100%;
  height: 10px;
  background-color: #e2e8f0;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 12px;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 5px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-percent {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.progress-time {
  font-size: 14px;
  color: #64748b;
  font-family: 'Courier New', monospace;
}

.progress-text {
  margin: 0;
  font-size: 14px;
  color: #475569;
  text-align: center;
  font-weight: 500;
}

.progress-features {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 8px;
  flex-wrap: wrap;
}

.feature-ready {
  font-size: 12px;
  color: #059669;
  background-color: rgba(5, 150, 105, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.feature-enabled {
  font-size: 12px;
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

/* 缓存状态样式 */
.cache-info {
  margin-top: 16px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.cache-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #374151;
}

.cache-info p {
  margin: 4px 0;
  font-size: 12px;
  color: #6b7280;
}

.cache-bar {
  width: 100%;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;
}

.cache-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 渐进式加载控制样式 */
.progressive-controls {
  margin-top: 16px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.progressive-controls h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #374151;
}

.progressive-controls label {
  font-size: 13px;
  color: #475569;
  cursor: pointer;
}

.progressive-controls input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

/* 选择框样式 */
select {
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background-color: #ffffff;
  color: #475569;
  font-size: 12px;
}

select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

</style>
