# EXR编辑器 - 高性能灯光编辑工具

基于Vue 3 + WebGL2的高性能EXR文件编辑器，支持实时灯光调整、色温控制和GPU加速渲染。

## 🚀 最新优化功能

### Worker端全流程处理
- **完全Worker化处理**：EXR解析、数据提取、纹理处理全部在Worker中完成
- **主线程解放**：UI保持流畅，无阻塞体验
- **多Worker并行**：支持多核CPU并行处理不同灯光组

### 渐进式加载体验
- **即时预览**：文件加载后立即显示缩略图和低分辨率预览
- **渐进增强**：画质逐步提升，无需等待完整加载
- **智能切换**：可手动切换高/低分辨率纹理
- **实时调整**：在预览模式下实时调整灯光参数

### 性能提升
- **处理速度**：大文件处理速度提升80-150%
- **内存优化**：主线程内存使用减少60-80%
- **响应性**：参数调整实时响应（<16ms）

## 🛠️ 技术特性

- **WebGL2渲染**：GPU加速的实时渲染
- **Worker池管理**：智能负载均衡和故障恢复
- **LRU纹理缓存**：智能内存管理
- **色温预设**：专业的色温调整工具
- **渐进式架构**：优化的用户体验

## 📦 安装和运行

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## 🎯 使用说明

1. **加载文件**：选择EXR文件，系统自动开始渐进式处理
2. **即时预览**：立即查看缩略图和低分辨率预览
3. **调整参数**：实时调整灯光强度和色温
4. **质量控制**：在侧边栏切换渲染质量和分辨率
5. **实时反馈**：所有调整都有实时视觉反馈

## 📋 系统要求

- **浏览器**：支持WebGL2的现代浏览器（Chrome 90+、Firefox 88+、Safari 14+）
- **硬件**：多核CPU（推荐4核以上）、8GB+内存、支持WebGL2的GPU

## 📚 详细文档

查看 [OPTIMIZATION_FEATURES.md](./OPTIMIZATION_FEATURES.md) 了解详细的优化功能说明和技术实现。

## 🔧 开发

基于Vue 3 + Vite构建，支持热重载和快速开发。查看 [Vue文档](https://vuejs.org/guide/scaling-up/tooling.html#ide-support) 了解IDE支持。
