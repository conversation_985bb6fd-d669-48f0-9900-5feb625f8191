// 色温计算工具和预计算查找表
// 提供高性能的色温转RGB转换

// 预计算的色温查找表 (1000K - 15000K，每100K一个值)
export const COLOR_TEMP_LUT = {};

// 生成色温查找表
function generateColorTempLUT() {
  for (let temp = 1000; temp <= 15000; temp += 100) {
    COLOR_TEMP_LUT[temp] = calculateColorTempRGB(temp);
  }
}

// 精确的色温转RGB计算函数
function calculateColorTempRGB(temp) {
  temp = Math.max(1000, Math.min(15000, temp));
  
  let r, g, b;
  
  // 红色通道计算
  if (temp < 6600) {
    r = 1.0;
  } else {
    r = 1.292936 * Math.pow(temp / 100.0 - 60.0, -0.1332047);
    r = Math.max(0.0, Math.min(1.0, r));
  }
  
  // 绿色通道计算
  if (temp < 6600) {
    g = 0.39008157 * Math.log(temp / 100.0) - 0.63184144;
  } else {
    g = 1.129890 * Math.pow(temp / 100.0 - 60.0, -0.0755148);
  }
  g = Math.max(0.0, Math.min(1.0, g));
  
  // 蓝色通道计算
  if (temp >= 6600) {
    b = 1.0;
  } else if (temp < 2000) {
    b = 0.0;
  } else {
    b = 0.543206789 * Math.log(temp / 100.0 - 10.0) - 1.19625408;
    b = Math.max(0.0, Math.min(1.0, b));
  }
  
  return [r, g, b];
}

// 快速色温转RGB（使用查找表和插值）
export function fastColorTempToRGB(temp) {
  temp = Math.max(1000, Math.min(15000, temp));
  
  // 找到最接近的预计算值
  const lowerTemp = Math.floor(temp / 100) * 100;
  const upperTemp = Math.ceil(temp / 100) * 100;
  
  // 如果正好匹配预计算值
  if (lowerTemp === upperTemp) {
    return COLOR_TEMP_LUT[lowerTemp] || calculateColorTempRGB(temp);
  }
  
  // 线性插值
  const lowerRGB = COLOR_TEMP_LUT[lowerTemp];
  const upperRGB = COLOR_TEMP_LUT[upperTemp];
  
  if (!lowerRGB || !upperRGB) {
    return calculateColorTempRGB(temp);
  }
  
  const factor = (temp - lowerTemp) / (upperTemp - lowerTemp);
  
  return [
    lowerRGB[0] + (upperRGB[0] - lowerRGB[0]) * factor,
    lowerRGB[1] + (upperRGB[1] - lowerRGB[1]) * factor,
    lowerRGB[2] + (upperRGB[2] - lowerRGB[2]) * factor
  ];
}

// 常用色温预设
export const COLOR_TEMP_PRESETS = {
  'Candle': 1900,
  'Tungsten 40W': 2600,
  'Tungsten 100W': 2850,
  'Halogen': 3200,
  'Carbon Arc': 5200,
  'High Noon Sun': 5400,
  'Direct Sunlight': 5800,
  'Overcast Sky': 6000,
  'Daylight': 6500,
  'Partly Cloudy': 6800,
  'Shade': 7500,
  'Heavily Overcast': 9000,
  'Blue Sky': 12000
};

// 获取预设色温列表
export function getColorTempPresets() {
  return Object.entries(COLOR_TEMP_PRESETS).map(([name, temp]) => ({
    name,
    temp,
    rgb: fastColorTempToRGB(temp)
  }));
}

// 批量转换色温数组
export function batchColorTempToRGB(temperatures) {
  return temperatures.map(temp => fastColorTempToRGB(temp));
}

// 创建色温渐变
export function createColorTempGradient(startTemp, endTemp, steps = 10) {
  const gradient = [];
  const stepSize = (endTemp - startTemp) / (steps - 1);
  
  for (let i = 0; i < steps; i++) {
    const temp = startTemp + stepSize * i;
    gradient.push({
      temperature: temp,
      rgb: fastColorTempToRGB(temp)
    });
  }
  
  return gradient;
}

// 找到最接近的预设色温
export function findClosestPreset(targetTemp) {
  let closest = null;
  let minDiff = Infinity;
  
  for (const [name, temp] of Object.entries(COLOR_TEMP_PRESETS)) {
    const diff = Math.abs(temp - targetTemp);
    if (diff < minDiff) {
      minDiff = diff;
      closest = { name, temp };
    }
  }
  
  return closest;
}

// 色温转换为开尔文显示字符串
export function formatTemperature(temp) {
  if (temp >= 1000) {
    return `${(temp / 1000).toFixed(1)}K`;
  }
  return `${Math.round(temp)}K`;
}

// 验证色温值
export function validateTemperature(temp) {
  const numTemp = Number(temp);
  if (isNaN(numTemp)) return false;
  return numTemp >= 1000 && numTemp <= 15000;
}

// 色温转换为CSS颜色字符串
export function colorTempToCSS(temp) {
  const [r, g, b] = fastColorTempToRGB(temp);
  return `rgb(${Math.round(r * 255)}, ${Math.round(g * 255)}, ${Math.round(b * 255)})`;
}

// 创建色温调色板
export function createColorTempPalette(count = 20) {
  const palette = [];
  const minTemp = 2000;
  const maxTemp = 12000;
  const step = (maxTemp - minTemp) / (count - 1);
  
  for (let i = 0; i < count; i++) {
    const temp = minTemp + step * i;
    palette.push({
      temperature: Math.round(temp),
      rgb: fastColorTempToRGB(temp),
      css: colorTempToCSS(temp),
      name: findClosestPreset(temp)?.name || `${Math.round(temp)}K`
    });
  }
  
  return palette;
}

// 初始化查找表
generateColorTempLUT();

// 导出计算函数供着色器使用
export function getColorTempShaderCode() {
  return `
// 优化的色温转换函数（使用预计算系数）
vec3 fastColorTempToRGB(float temp) {
  temp = clamp(temp, 1000.0, 15000.0);
  
  float r, g, b;
  
  // 使用分段多项式逼近，比原始算法更快
  if (temp < 6600.0) {
    r = 1.0;
    g = clamp(0.39008157 * log(temp * 0.01) - 0.63184144, 0.0, 1.0);
  } else {
    float t = temp * 0.01 - 60.0;
    r = clamp(1.292936 * pow(t, -0.1332047), 0.0, 1.0);
    g = clamp(1.129890 * pow(t, -0.0755148), 0.0, 1.0);
  }
  
  if (temp >= 6600.0) {
    b = 1.0;
  } else if (temp < 2000.0) {
    b = 0.0;
  } else {
    b = clamp(0.543206789 * log(temp * 0.01 - 10.0) - 1.19625408, 0.0, 1.0);
  }
  
  return vec3(r, g, b);
}
`;
}

console.log('Color temperature LUT generated with', Object.keys(COLOR_TEMP_LUT).length, 'entries');
