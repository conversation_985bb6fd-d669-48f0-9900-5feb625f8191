// EXR处理管理器 - 协调Worker池和渐进式加载

import { globalWorkerPool } from './WorkerPool.js';
import { globalTextureCache } from './TextureCache.js';

export class ExrProcessorManager {
  constructor() {
    this.workerPool = globalWorkerPool;
    this.textureCache = globalTextureCache;
    this.currentFile = null;
    this.processingState = {
      isProcessing: false,
      stage: '',
      progress: 0,
      totalStages: 0,
      currentStage: 0
    };
    this.callbacks = {
      onProgress: null,
      onStageChange: null,
      onThumbnailReady: null,
      onPreviewReady: null,
      onGroupReady: null,
      onComplete: null,
      onError: null
    };
  }

  // 设置回调函数
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // 触发回调
  _triggerCallback(name, ...args) {
    if (this.callbacks[name]) {
      try {
        this.callbacks[name](...args);
      } catch (error) {
        console.error(`Callback ${name} error:`, error);
      }
    }
  }

  // 更新处理状态
  _updateProgress(stage, progress, currentStage = null) {
    this.processingState.stage = stage;
    this.processingState.progress = progress;
    if (currentStage !== null) {
      this.processingState.currentStage = currentStage;
    }
    
    this._triggerCallback('onProgress', { ...this.processingState });
    
    if (stage !== this.processingState.stage) {
      this._triggerCallback('onStageChange', stage);
    }
  }

  // 处理EXR文件（完整流程）
  async processExrFile(file, options = {}) {
    const {
      generateThumbnail = true,
      generatePreview = true,
      thumbnailSize = 256,
      previewSize = 512,
      enableParallelProcessing = true,
      maxConcurrentGroups = 3
    } = options;

    try {
      this.processingState.isProcessing = true;
      this.processingState.totalStages = 4; // 分析、缩略图、预览、灯光组处理
      this.processingState.currentStage = 0;

      // 读取文件
      this._updateProgress('读取文件...', 0, 1);
      const arrayBuffer = await file.arrayBuffer();
      this.currentFile = { name: file.name, size: file.size, arrayBuffer };

      // 阶段1: 分析EXR文件结构
      this._updateProgress('分析EXR文件结构...', 10, 1);
      const analysisResult = await this.workerPool.executeTask('PROCESS_EXR_FULL', {
        arrayBuffer: arrayBuffer.slice(0) // 创建副本避免传输问题
      }, 10); // 高优先级

      if (!analysisResult.success) {
        throw new Error('EXR文件分析失败');
      }

      const { width, height, lightGroups } = analysisResult;
      this._updateProgress('文件分析完成', 20, 1);

      // 阶段2: 生成缩略图（如果需要）
      if (generateThumbnail) {
        this._updateProgress('生成缩略图...', 25, 2);
        try {
          const thumbnailResult = await this.workerPool.executeTask('GENERATE_THUMBNAIL', {
            arrayBuffer: arrayBuffer.slice(0),
            maxSize: thumbnailSize
          }, 8); // 高优先级

          if (thumbnailResult.success) {
            this._triggerCallback('onThumbnailReady', {
              data: new Float32Array(thumbnailResult.thumbnailData),
              width: thumbnailResult.width,
              height: thumbnailResult.height
            });
          }
        } catch (thumbnailError) {
          console.warn('缩略图生成失败:', thumbnailError);
        }
      }
      this._updateProgress('缩略图生成完成', 35, 2);

      // 阶段3: 生成预览（如果需要）
      if (generatePreview) {
        this._updateProgress('生成预览图像...', 40, 3);
        try {
          const previewResult = await this.workerPool.executeTask('GENERATE_PREVIEW', {
            arrayBuffer: arrayBuffer.slice(0),
            previewWidth: previewSize,
            previewHeight: previewSize
          }, 7); // 中高优先级

          if (previewResult.success && previewResult.previewGroups.length > 0) {
            // 处理预览组数据
            const previewGroups = previewResult.previewGroups.map(group => ({
              name: group.name,
              data: new Float32Array(group.data),
              width: group.width,
              height: group.height
            }));

            this._triggerCallback('onPreviewReady', {
              groups: previewGroups,
              width: previewResult.width,
              height: previewResult.height
            });
          }
        } catch (previewError) {
          console.warn('预览生成失败:', previewError);
        }
      }
      this._updateProgress('预览生成完成', 50, 3);

      // 阶段4: 处理灯光组（并行或串行）
      this._updateProgress('处理灯光组...', 55, 4);
      
      if (enableParallelProcessing && lightGroups.length > 1) {
        await this._processLightGroupsParallel(lightGroups, arrayBuffer, maxConcurrentGroups);
      } else {
        await this._processLightGroupsSequential(lightGroups, arrayBuffer);
      }

      this._updateProgress('处理完成', 100, 4);
      this._triggerCallback('onComplete', {
        width,
        height,
        lightGroups: lightGroups.map(g => g.name),
        totalGroups: lightGroups.length
      });

    } catch (error) {
      console.error('EXR处理失败:', error);
      this._triggerCallback('onError', error);
      throw error;
    } finally {
      this.processingState.isProcessing = false;
    }
  }

  // 并行处理灯光组
  async _processLightGroupsParallel(lightGroups, arrayBuffer, maxConcurrentGroups) {
    const totalGroups = lightGroups.length;
    let processedGroups = 0;

    // 创建任务批次
    const tasks = lightGroups.map((group, index) => ({
      type: 'PROCESS_LIGHT_GROUP',
      data: {
        arrayBuffer: arrayBuffer.slice(0),
        groupName: group.name,
        channels: group.channels,
        width: this.currentFile.width,
        height: this.currentFile.height,
        generateLowRes: true
      },
      priority: 5 - Math.floor(index / maxConcurrentGroups), // 降低优先级
      groupIndex: index,
      groupName: group.name
    }));

    // 分批处理
    for (let i = 0; i < tasks.length; i += maxConcurrentGroups) {
      const batch = tasks.slice(i, i + maxConcurrentGroups);
      
      // 并行执行当前批次
      const batchPromises = batch.map(async (task) => {
        try {
          const result = await this.workerPool.executeTask(task.type, task.data, task.priority);
          
          if (result.success) {
            // 创建纹理数据
            const textureData = new Float32Array(result.textureData);
            
            // 触发组就绪回调
            this._triggerCallback('onGroupReady', {
              groupName: result.groupName,
              textureData,
              width: result.width,
              height: result.height,
              lowResData: result.lowResData ? {
                data: new Float32Array(result.lowResData.data),
                width: result.lowResData.width,
                height: result.lowResData.height
              } : null
            });

            processedGroups++;
            const progress = 55 + (processedGroups / totalGroups) * 40; // 55-95%
            this._updateProgress(`处理灯光组 ${processedGroups}/${totalGroups}`, progress, 4);
          }
          
          return result;
        } catch (error) {
          console.error(`处理灯光组 ${task.groupName} 失败:`, error);
          processedGroups++;
          return { success: false, error: error.message, groupName: task.groupName };
        }
      });

      // 等待当前批次完成
      await Promise.allSettled(batchPromises);
    }
  }

  // 串行处理灯光组
  async _processLightGroupsSequential(lightGroups, arrayBuffer) {
    const totalGroups = lightGroups.length;

    for (let i = 0; i < lightGroups.length; i++) {
      const group = lightGroups[i];
      
      try {
        const result = await this.workerPool.executeTask('PROCESS_LIGHT_GROUP', {
          arrayBuffer: arrayBuffer.slice(0),
          groupName: group.name,
          channels: group.channels,
          width: this.currentFile.width,
          height: this.currentFile.height,
          generateLowRes: true
        }, 5);

        if (result.success) {
          // 创建纹理数据
          const textureData = new Float32Array(result.textureData);
          
          // 触发组就绪回调
          this._triggerCallback('onGroupReady', {
            groupName: result.groupName,
            textureData,
            width: result.width,
            height: result.height,
            lowResData: result.lowResData ? {
              data: new Float32Array(result.lowResData.data),
              width: result.lowResData.width,
              height: result.lowResData.height
            } : null
          });
        }

        const progress = 55 + ((i + 1) / totalGroups) * 40; // 55-95%
        this._updateProgress(`处理灯光组 ${i + 1}/${totalGroups}`, progress, 4);

      } catch (error) {
        console.error(`处理灯光组 ${group.name} 失败:`, error);
      }
    }
  }

  // 获取处理状态
  getProcessingState() {
    return { ...this.processingState };
  }

  // 取消当前处理
  cancelProcessing() {
    // 注意：这里只是标记取消，实际的Worker任务可能仍在执行
    this.processingState.isProcessing = false;
    this._triggerCallback('onError', new Error('Processing cancelled by user'));
  }

  // 清理资源
  cleanup() {
    this.currentFile = null;
    this.processingState.isProcessing = false;
  }
}

// 创建全局实例
export const globalExrProcessor = new ExrProcessorManager();
