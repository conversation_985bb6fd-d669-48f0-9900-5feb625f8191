// 简化版EXR处理Worker - 避免模块加载问题
// 这个版本使用传统的Worker脚本加载方式

let exrModule = null;

// 简化的EXR模块加载
function loadExrModule() {
  return new Promise((resolve, reject) => {
    if (exrModule) {
      resolve(exrModule);
      return;
    }

    // 创建script标签来加载WASM模块
    const loadScript = () => {
      fetch('/wasm/exr_bridge.js')
        .then(response => response.text())
        .then(scriptContent => {
          // 在Worker中执行脚本
          try {
            eval(scriptContent);
            
            if (self.ExrModule) {
              self.ExrModule().then(module => {
                exrModule = module;
                resolve(module);
              }).catch(reject);
            } else {
              reject(new Error('ExrModule not found after loading script'));
            }
          } catch (error) {
            reject(new Error('Failed to execute EXR script: ' + error.message));
          }
        })
        .catch(reject);
    };

    loadScript();
  });
}

// 处理EXR文件基本信息
async function processExrInfo(arrayBuffer) {
  try {
    const Module = await loadExrModule();
    const tempFileName = 'worker_' + Date.now() + '.exr';
    
    Module.FS_createDataFile('/', tempFileName, new Uint8Array(arrayBuffer), true, true);
    
    const width = Module.get_exr_width(tempFileName);
    const height = Module.get_exr_height(tempFileName);
    const channels = Module.get_exr_channels(tempFileName);
    
    // 简化的通道处理
    let jsChannels = [];
    if (channels && typeof channels.size === 'function') {
      const n = channels.size();
      for (let i = 0; i < n; i++) {
        jsChannels.push(channels.get(i));
      }
    }
    
    // 分析灯光组
    const lightGroups = [];
    const channelMap = {};
    
    for (const ch of jsChannels) {
      const match = ch.match(/^(.*)\.[RGB]$/i);
      const group = match ? match[1] : ch;
      if (!channelMap[group]) channelMap[group] = [];
      channelMap[group].push(ch);
    }
    
    for (const group in channelMap) {
      if (/light|Light|VRayLight|layer|Layer|AOV|aov/.test(group)) {
        const channelsInGroup = channelMap[group];
        const hasR = channelsInGroup.some(ch => ch.endsWith('.R'));
        const hasG = channelsInGroup.some(ch => ch.endsWith('.G'));
        const hasB = channelsInGroup.some(ch => ch.endsWith('.B'));
        
        if (hasR && hasG && hasB) {
          lightGroups.push({
            name: group,
            channels: channelsInGroup
          });
        }
      }
    }
    
    Module.FS_unlink('/' + tempFileName);
    
    return {
      success: true,
      width,
      height,
      lightGroups
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// 处理单个通道数据
async function processChannelData(arrayBuffer, channelName) {
  try {
    const Module = await loadExrModule();
    const tempFileName = 'worker_channel_' + Date.now() + '.exr';
    
    Module.FS_createDataFile('/', tempFileName, new Uint8Array(arrayBuffer), true, true);
    
    const channelData = Module.get_exr_channel(tempFileName, channelName);
    
    let floatArray = null;
    if (channelData && typeof channelData.size === 'function') {
      const n = channelData.size();
      floatArray = new Float32Array(n);
      for (let i = 0; i < n; i++) {
        floatArray[i] = channelData.get(i);
      }
    }
    
    Module.FS_unlink('/' + tempFileName);
    
    return {
      success: true,
      channelName,
      data: floatArray
    };
    
  } catch (error) {
    return {
      success: false,
      channelName,
      error: error.message
    };
  }
}

// Worker消息处理
self.onmessage = async function(e) {
  const { type, data, id } = e.data;
  
  try {
    switch (type) {
      case 'PROCESS_EXR_INFO':
        const info = await processExrInfo(data.arrayBuffer);
        self.postMessage({
          type: 'EXR_INFO_RESULT',
          id,
          data: info
        });
        break;
        
      case 'PROCESS_CHANNEL_DATA':
        const channelResult = await processChannelData(data.arrayBuffer, data.channelName);
        self.postMessage({
          type: 'CHANNEL_DATA_RESULT',
          id,
          data: channelResult
        });
        break;
        
      default:
        self.postMessage({
          type: 'ERROR',
          id,
          data: { error: 'Unknown message type: ' + type }
        });
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      id,
      data: { error: error.message }
    });
  }
};

// 初始化完成通知
self.postMessage({
  type: 'WORKER_READY'
});
