// Worker池管理器 - 支持多Worker并行处理和负载均衡

export class WorkerPool {
  constructor(workerScript, maxWorkers = navigator.hardwareConcurrency || 4) {
    this.workerScript = workerScript;
    this.maxWorkers = Math.min(maxWorkers, 8); // 限制最大Worker数量
    this.workers = [];
    this.availableWorkers = [];
    this.busyWorkers = new Set();
    this.taskQueue = [];
    this.taskId = 0;
    this.pendingTasks = new Map();
    this.isInitialized = false;
    this.initPromise = null;
  }

  // 初始化Worker池
  async initialize() {
    if (this.isInitialized) return;
    if (this.initPromise) return this.initPromise;

    this.initPromise = this._doInitialize();
    await this.initPromise;
  }

  async _doInitialize() {
    console.log(`Initializing worker pool with ${this.maxWorkers} workers...`);
    
    const initPromises = [];
    
    for (let i = 0; i < this.maxWorkers; i++) {
      const worker = new Worker(this.workerScript);
      const workerId = i;
      
      worker.workerId = workerId;
      worker.isReady = false;
      worker.currentTask = null;
      
      // 设置消息处理
      worker.onmessage = (e) => this._handleWorkerMessage(worker, e);
      worker.onerror = (error) => this._handleWorkerError(worker, error);
      
      this.workers.push(worker);
      
      // 等待Worker初始化
      const initPromise = new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error(`Worker ${workerId} initialization timeout`));
        }, 10000);
        
        const messageHandler = (e) => {
          if (e.data.type === 'WORKER_READY') {
            clearTimeout(timeout);
            worker.removeEventListener('message', messageHandler);
            worker.isReady = true;
            this.availableWorkers.push(worker);
            resolve();
          }
        };
        
        worker.addEventListener('message', messageHandler);
        
        // 发送初始化消息
        worker.postMessage({
          type: 'INIT_WORKER',
          workerId,
          id: this.taskId++
        });
      });
      
      initPromises.push(initPromise);
    }
    
    try {
      await Promise.all(initPromises);
      this.isInitialized = true;
      console.log(`Worker pool initialized successfully with ${this.workers.length} workers`);
    } catch (error) {
      console.error('Failed to initialize worker pool:', error);
      throw error;
    }
  }

  // 处理Worker消息
  _handleWorkerMessage(worker, e) {
    const { type, id, workerId, data } = e.data;
    
    // 处理任务完成
    if (this.pendingTasks.has(id)) {
      const { resolve, reject } = this.pendingTasks.get(id);
      this.pendingTasks.delete(id);
      
      // 释放Worker
      this._releaseWorker(worker);
      
      if (type === 'ERROR') {
        reject(new Error(data.error));
      } else {
        resolve(data);
      }
    }
    
    // 处理下一个任务
    this._processNextTask();
  }

  // 处理Worker错误
  _handleWorkerError(worker, error) {
    console.error(`Worker ${worker.workerId} error:`, error);
    
    // 如果Worker有当前任务，标记为失败
    if (worker.currentTask) {
      const { id, reject } = worker.currentTask;
      if (this.pendingTasks.has(id)) {
        this.pendingTasks.delete(id);
        reject(new Error(`Worker error: ${error.message}`));
      }
    }
    
    // 释放Worker
    this._releaseWorker(worker);
    
    // 尝试重启Worker
    this._restartWorker(worker);
  }

  // 重启Worker
  async _restartWorker(worker) {
    try {
      worker.terminate();
      
      // 创建新Worker
      const newWorker = new Worker(this.workerScript);
      newWorker.workerId = worker.workerId;
      newWorker.isReady = false;
      newWorker.currentTask = null;
      
      newWorker.onmessage = (e) => this._handleWorkerMessage(newWorker, e);
      newWorker.onerror = (error) => this._handleWorkerError(newWorker, error);
      
      // 替换旧Worker
      const index = this.workers.indexOf(worker);
      if (index !== -1) {
        this.workers[index] = newWorker;
      }
      
      // 等待新Worker初始化
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error(`Worker ${newWorker.workerId} restart timeout`));
        }, 5000);
        
        const messageHandler = (e) => {
          if (e.data.type === 'WORKER_READY') {
            clearTimeout(timeout);
            newWorker.removeEventListener('message', messageHandler);
            newWorker.isReady = true;
            this.availableWorkers.push(newWorker);
            resolve();
          }
        };
        
        newWorker.addEventListener('message', messageHandler);
        newWorker.postMessage({
          type: 'INIT_WORKER',
          workerId: newWorker.workerId,
          id: this.taskId++
        });
      });
      
      console.log(`Worker ${newWorker.workerId} restarted successfully`);
      
    } catch (error) {
      console.error(`Failed to restart worker ${worker.workerId}:`, error);
    }
  }

  // 执行任务
  async executeTask(taskType, taskData, priority = 0) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const id = this.taskId++;
      const task = {
        id,
        type: taskType,
        data: taskData,
        priority,
        resolve,
        reject,
        createdAt: Date.now()
      };

      this.pendingTasks.set(id, { resolve, reject });
      
      // 添加到任务队列（按优先级排序）
      this.taskQueue.push(task);
      this.taskQueue.sort((a, b) => b.priority - a.priority);
      
      // 尝试立即处理任务
      this._processNextTask();
      
      // 设置超时
      setTimeout(() => {
        if (this.pendingTasks.has(id)) {
          this.pendingTasks.delete(id);
          reject(new Error('Task timeout'));
        }
      }, 60000); // 60秒超时
    });
  }

  // 处理下一个任务
  _processNextTask() {
    if (this.taskQueue.length === 0 || this.availableWorkers.length === 0) {
      return;
    }

    const task = this.taskQueue.shift();
    const worker = this.availableWorkers.shift();
    
    // 标记Worker为忙碌
    this.busyWorkers.add(worker);
    worker.currentTask = task;
    
    // 发送任务到Worker
    worker.postMessage({
      type: task.type,
      data: task.data,
      id: task.id,
      workerId: worker.workerId
    });
  }

  // 释放Worker
  _releaseWorker(worker) {
    worker.currentTask = null;
    this.busyWorkers.delete(worker);
    
    if (worker.isReady && !this.availableWorkers.includes(worker)) {
      this.availableWorkers.push(worker);
    }
  }

  // 获取池状态
  getStatus() {
    return {
      totalWorkers: this.workers.length,
      availableWorkers: this.availableWorkers.length,
      busyWorkers: this.busyWorkers.size,
      queuedTasks: this.taskQueue.length,
      pendingTasks: this.pendingTasks.size,
      isInitialized: this.isInitialized
    };
  }

  // 清理资源
  terminate() {
    for (const worker of this.workers) {
      worker.terminate();
    }
    
    this.workers = [];
    this.availableWorkers = [];
    this.busyWorkers.clear();
    this.taskQueue = [];
    
    // 拒绝所有待处理的任务
    for (const [id, { reject }] of this.pendingTasks) {
      reject(new Error('Worker pool terminated'));
    }
    this.pendingTasks.clear();
    
    this.isInitialized = false;
  }

  // 批量执行任务
  async executeBatch(tasks, maxConcurrency = null) {
    const concurrency = maxConcurrency || this.maxWorkers;
    const results = [];
    
    for (let i = 0; i < tasks.length; i += concurrency) {
      const batch = tasks.slice(i, i + concurrency);
      const batchPromises = batch.map(task => 
        this.executeTask(task.type, task.data, task.priority || 0)
      );
      
      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults);
    }
    
    return results;
  }
}

// 创建全局Worker池实例
export const globalWorkerPool = new WorkerPool(
  new URL('../workers/exr-worker-simple.js', import.meta.url),
  Math.min(navigator.hardwareConcurrency || 4, 6) // 最多6个Worker
);
