#version 300 es
// vertexShader.vert
// A simple vertex shader to draw a screen-filling quad and pass texture coordinates.
// Using GLSL ES 3.0 syntax for WebGL2.

in vec4 a_position; // Vertex position (e.g., [-1, -1, 0, 1] to [1, 1, 0, 1])
in vec2 a_texCoord; // Texture coordinates (e.g., [0, 0] to [1, 1])

out vec2 v_texCoord; // Pass texture coordinates to the fragment shader

void main() {
  // Set the position of the vertex. Since we're drawing a screen-filling
  // quad from -1 to 1 in clip space, the position attribute directly maps
  // to gl_Position.
  gl_Position = a_position;

  // Pass the texture coordinates to the fragment shader.
  v_texCoord = a_texCoord;
}
